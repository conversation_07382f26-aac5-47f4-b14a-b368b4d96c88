import { Hono } from "hono";
import { contextStorage } from "hono/context-storage";
import { apWebhookProcessor } from "./webhooks/apProcessor";
import { ccWebhookProcessor } from "./webhooks/ccProcessor";

const app = new Hono<Env>();
app.use(contextStorage());

app.onError((err, c) => {
	console.error(err);
	return c.json(
		{
			message: "Internal Server Error",
			requestId: crypto.randomUUID(),
			timestamp: new Date().toISOString(),
		},
		500,
	);
});

// CliniCore webhook endpoint (replaces Socket.io)
app.post("/webhooks/cc", ccWebhookProcessor);

// AutoPatient webhook endpoints (bidirectional sync)
app.post("/webhooks/ap/appointment/creates", apWebhookProcessor);
app.post("/webhooks/ap/appointment/updates", apWebhookProcessor);

// Health check endpoints
app.get("/", (c) => c.text("DermaCare Data Sync Service - OK"));
app.get("/health", (c) =>
	c.json({
		status: "healthy",
		timestamp: new Date().toISOString(),
		version: "2.0.0",
	}),
);

export default app;
