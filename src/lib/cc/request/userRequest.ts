import type { GetCCUserType } from "@libCC/CCTypes";
import { ccApiClient } from "./request";

/**
 * CC User Request methods
 *
 * This module contains all user-specific API request methods for the CC platform.
 * It provides a clean interface for making user-related API calls with proper
 * caching, error handling, and data transformation.
 */
export class CCUserRequest {
	/**
	 * Get a user by ID
	 * @param id - User ID
	 * @returns Promise with user data
	 */
	async getUser(id: number): Promise<GetCCUserType> {
		const response = await ccApiClient.get<{ user: GetCCUserType }>(
			`/users/${id}`,
			undefined,
			`cc:user:${id}`,
		);
		return response.data.user;
	}

	/**
	 * Get all users
	 * @returns Promise with users array
	 */
	async getAllUsers(): Promise<GetCCUserType[]> {
		const response = await ccApiClient.get<{ users: GetCCUserType[] }>(
			"/users",
			undefined,
			"cc:users",
		);
		return response.data.users;
	}

	/**
	 * Get user by username
	 * @param username - <PERSON>rname
	 * @returns Promise with user data or null
	 */
	async getUserByUsername(username: string): Promise<GetCCUserType | null> {
		const users = await this.getAllUsers();
		return users.find((user) => user.username === username) || null;
	}

	/**
	 * Get user by email
	 * @param email - Email address
	 * @returns Promise with user data or null
	 */
	async getUserByEmail(email: string): Promise<GetCCUserType | null> {
		const users = await this.getAllUsers();
		return users.find((user) => user.email === email) || null;
	}
}

// Export singleton instance
export const ccUserRequest = new CCUserRequest();
