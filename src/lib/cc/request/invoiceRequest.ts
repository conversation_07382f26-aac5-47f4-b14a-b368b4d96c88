import type { GetInvoiceType } from "@libCC/CCTypes";
import { ccApiClient } from "./request";

/**
 * CC Invoice Request methods
 *
 * This module contains all invoice-specific API request methods for the CC platform.
 * It provides a clean interface for making invoice-related API calls with proper
 * caching, error handling, and data transformation.
 */
export class CCInvoiceRequest {
	/**
	 * Get invoices by IDs
	 * @param ids - Array of invoice IDs
	 * @returns Promise with invoices array
	 */
	async getInvoices(ids: number[]): Promise<GetInvoiceType[]> {
		const queryString = this.idsToQueryString(ids);
		const response = await ccApiClient.get<{ invoices: GetInvoiceType[] }>(
			`/invoices?${queryString}`,
			undefined,
			`cc:invoices:${queryString}`,
		);
		return response.data.invoices;
	}

	/**
	 * Get a single invoice by ID
	 * @param id - Invoice ID
	 * @returns Promise with invoice data
	 */
	async getInvoice(id: number): Promise<GetInvoiceType> {
		const invoices = await this.getInvoices([id]);
		return invoices[0];
	}

	/**
	 * Get invoices by patient ID
	 * @param patientId - Patient ID
	 * @returns Promise with invoices array
	 */
	async getInvoicesByPatient(patientId: number): Promise<GetInvoiceType[]> {
		const response = await ccApiClient.get<{ invoices: GetInvoiceType[] }>(
			`/invoices`,
			{ patient: patientId },
			`cc:invoices:patient:${patientId}`,
		);
		return response.data.invoices;
	}

	/**
	 * Convert array of IDs to query string format
	 * @param ids - Array of IDs
	 * @returns Query string
	 */
	private idsToQueryString(ids: number[]): string {
		return ids
			.map((id) => `ids[]=${id.toString().trim()}`)
			.join("&")
			.trim();
	}
}

// Export singleton instance
export const ccInvoiceRequest = new CCInvoiceRequest();
