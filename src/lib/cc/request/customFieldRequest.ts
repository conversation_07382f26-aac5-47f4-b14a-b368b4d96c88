import type { GetCCCustomField } from "@libCC/CCTypes";
import { ccApiClient } from "./request";

/**
 * CC Custom Field Request methods
 *
 * This module contains all custom field-specific API request methods for the CC platform.
 * It provides a clean interface for making custom field-related API calls with proper
 * caching, error handling, and data transformation.
 */
export class CCCustomFieldRequest {
	/**
	 * Get a custom field by ID
	 * @param id - Custom field ID
	 * @returns Promise with custom field data
	 */
	async getCustomField(id: number): Promise<GetCCCustomField> {
		const response = await ccApiClient.get<{ customField: GetCCCustomField }>(
			`/customFields/${id}`,
			undefined,
			`cc:customField:${id}`,
		);
		return response.data.customField;
	}

	/**
	 * Get all custom fields
	 * @returns Promise with custom fields array
	 */
	async getAllCustomFields(): Promise<GetCCCustomField[]> {
		const response = await ccApiClient.get<{
			customFields: GetCCCustomField[];
		}>("/customFields/", undefined, "cc:customFields");
		return response.data.customFields;
	}

	/**
	 * Get custom field by name
	 * @param name - Custom field name
	 * @returns Promise with custom field data or null
	 */
	async getCustomFieldByName(name: string): Promise<GetCCCustomField | null> {
		const customFields = await this.getAllCustomFields();
		return customFields.find((field) => field.name === name) || null;
	}

	/**
	 * Get custom field by label
	 * @param label - Custom field label
	 * @returns Promise with custom field data or null
	 */
	async getCustomFieldByLabel(label: string): Promise<GetCCCustomField | null> {
		const customFields = await this.getAllCustomFields();
		return customFields.find((field) => field.label === label) || null;
	}
}

// Export singleton instance
export const ccCustomFieldRequest = new CCCustomFieldRequest();
