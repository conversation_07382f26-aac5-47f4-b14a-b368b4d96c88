import type { GetPaymentType } from "@libCC/CCTypes";
import { ccApiClient } from "./request";

/**
 * CC Payment Request methods
 *
 * This module contains all payment-specific API request methods for the CC platform.
 * It provides a clean interface for making payment-related API calls with proper
 * caching, error handling, and data transformation.
 */
export class CCPaymentRequest {
	/**
	 * Get payments by IDs
	 * @param ids - Array of payment IDs
	 * @returns Promise with payments array
	 */
	async getPayments(ids: number[]): Promise<GetPaymentType[]> {
		const queryString = this.idsToQueryString(ids);
		const response = await ccApiClient.get<{ payments: GetPaymentType[] }>(
			`/payments?${queryString}`,
			undefined,
			`cc:payments:${queryString}`,
		);
		return response.data.payments;
	}

	/**
	 * Get a single payment by ID
	 * @param id - Payment ID
	 * @returns Promise with payment data
	 */
	async getPayment(id: number): Promise<GetPaymentType> {
		const payments = await this.getPayments([id]);
		return payments[0];
	}

	/**
	 * Get payments by patient ID
	 * @param patientId - Patient ID
	 * @returns Promise with payments array
	 */
	async getPaymentsByPatient(patientId: number): Promise<GetPaymentType[]> {
		const response = await ccApiClient.get<{ payments: GetPaymentType[] }>(
			`/payments`,
			{ patient: patientId },
			`cc:payments:patient:${patientId}`,
		);
		return response.data.payments;
	}

	/**
	 * Get payments by date range
	 * @param startDate - Start date
	 * @param endDate - End date
	 * @returns Promise with payments array
	 */
	async getPaymentsByDateRange(
		startDate: string,
		endDate: string,
	): Promise<GetPaymentType[]> {
		const response = await ccApiClient.get<{ payments: GetPaymentType[] }>(
			`/payments`,
			{ startDate, endDate },
			`cc:payments:dateRange:${startDate}:${endDate}`,
		);
		return response.data.payments;
	}

	/**
	 * Convert array of IDs to query string format
	 * @param ids - Array of IDs
	 * @returns Query string
	 */
	private idsToQueryString(ids: number[]): string {
		return ids
			.map((id) => `ids[]=${id.toString().trim()}`)
			.join("&")
			.trim();
	}
}

// Export singleton instance
export const ccPaymentRequest = new CCPaymentRequest();
