import type {
	GetAPAppointmentType,
	PostAPAppointmentType,
	PutAPAppointmentType,
} from "@libAP/APTypes";
import { cleanData } from "@utils";
import { apApiClient } from "./baseRequest";

/**
 * AP Appointment Request methods
 *
 * This module contains all appointment-specific API request methods for the AP platform.
 * It provides a clean interface for making appointment-related API calls with proper
 * caching, error handling, and data transformation.
 */
export class APAppointmentRequest {
	/**
	 * Get an appointment by ID
	 * @param apId - Appointment ID
	 * @returns Promise with appointment data
	 */
	async get(apId: string): Promise<GetAPAppointmentType> {
		const response = await apApiClient.get<{
			appointment: GetAPAppointmentType;
		}>(
			`/calendars/events/appointments/${apId}`,
			undefined,
			`ap:appointment:${apId}`,
		);
		return response.data.appointment;
	}

	/**
	 * Create a new appointment
	 * @param payload - Appointment data to create
	 * @param calendarId - Calendar ID
	 * @param locationId - Location ID
	 * @returns Promise with created appointment
	 */
	async post(
		payload: PostAPAppointmentType,
		calendarId: string,
		locationId: string,
	): Promise<GetAPAppointmentType> {
		const response = await apApiClient.post<GetAPAppointmentType>(
			"/calendars/events/appointments",
			{ calendarId, ...cleanData(payload), locationId },
		);
		return response.data;
	}

	/**
	 * Create a block slot appointment
	 * @param payload - Block slot data
	 * @param calendarId - Calendar ID
	 * @param locationId - Location ID
	 * @returns Promise with created block slot
	 */
	async postBlockSlot(
		payload: PostAPAppointmentType,
		calendarId: string,
		locationId: string,
	): Promise<GetAPAppointmentType> {
		const response = await apApiClient.post<GetAPAppointmentType>(
			"/calendars/events/block-slots",
			{ calendarId, ...cleanData(payload), locationId },
		);
		return response.data;
	}

	/**
	 * Update an existing appointment
	 * @param apId - Appointment ID
	 * @param payload - Appointment data to update
	 * @param calendarId - Calendar ID
	 * @param locationId - Location ID
	 * @returns Promise with updated appointment
	 */
	async put(
		apId: string,
		payload: PutAPAppointmentType,
		calendarId: string,
		locationId: string,
	): Promise<GetAPAppointmentType> {
		const response = await apApiClient.put<GetAPAppointmentType>(
			`/calendars/events/appointments/${apId}`,
			{ calendarId, ...cleanData(payload), locationId },
		);
		return response.data;
	}

	/**
	 * Delete an appointment
	 * @param apId - Appointment ID
	 * @returns Promise with deletion result
	 */
	async delete(apId: string): Promise<boolean> {
		const response = await apApiClient.delete<{ succeeded: boolean }>(
			`/calendars/events/${apId}`,
		);
		return response.data.succeeded;
	}

	/**
	 * Get appointments for a calendar
	 * @param calendarId - Calendar ID
	 * @param params - Query parameters
	 * @returns Promise with appointments array
	 */
	async getCalendarAppointments(
		calendarId: string,
		params: {
			startTime?: string;
			endTime?: string;
			limit?: number;
		} = {},
	): Promise<GetAPAppointmentType[]> {
		const response = await apApiClient.get<{
			appointments: GetAPAppointmentType[];
		}>(
			`/calendars/${calendarId}/events/appointments`,
			cleanData(params),
			`ap:calendar:${calendarId}:appointments:${JSON.stringify(params)}`,
		);
		return response.data.appointments;
	}

	/**
	 * Get appointments within a date range
	 * @param calendarId - Calendar ID
	 * @param startTime - Start time
	 * @param endTime - End time
	 * @returns Promise with appointments array
	 */
	async getAppointmentsByDateRange(
		calendarId: string,
		startTime: string,
		endTime: string,
	): Promise<GetAPAppointmentType[]> {
		return this.getCalendarAppointments(calendarId, { startTime, endTime });
	}

	/**
	 * Get upcoming appointments
	 * @param calendarId - Calendar ID
	 * @param limit - Number of appointments to return
	 * @returns Promise with appointments array
	 */
	async getUpcomingAppointments(
		calendarId: string,
		limit: number = 20,
	): Promise<GetAPAppointmentType[]> {
		const now = new Date().toISOString();
		return this.getCalendarAppointments(calendarId, { startTime: now, limit });
	}
}

// Export singleton instance
export const apAppointmentRequest = new APAppointmentRequest();
