import type {
  GetAPContactType,
  PostAPContactType,
  GetAPAppointmentType,
} from "@libAP/APTypes";
import { cleanData } from "@utils";
import { apApiClient } from "./baseRequest";

/**
 * AP Contact Request methods
 *
 * This module contains all contact-specific API request methods for the AP platform.
 * It provides a clean interface for making contact-related API calls with proper
 * caching, error handling, and data transformation.
 */
export class APContactRequest {
  /**
   * Get a contact by ID
   * @param id - Contact ID
   * @returns Promise with contact data
   */
  async get(id: string): Promise<GetAPContactType> {
    const response = await apApiClient.get<{ contact: GetAPContactType }>(
      `/contacts/${id}`,
      undefined,
      `ap:contact:${id}`
    );
    return response.data.contact;
  }

  /**
   * Create a new contact
   * @param data - Contact data to create
   * @param locationId - Location ID
   * @returns Promise with created contact
   */
  async create(
    data: PostAPContactType,
    locationId: string
  ): Promise<GetAPContactType> {
    const response = await apApiClient.post<{ contact: GetAPContactType }>(
      "/contacts/",
      { ...cleanData(data), locationId }
    );
    return response.data.contact;
  }

  /**
   * Upsert a contact (create if not exists, update if exists)
   * @param data - Contact data to upsert
   * @param locationId - Location ID
   * @returns Promise with upserted contact
   */
  async upsert(
    data: PostAPContactType,
    locationId: string
  ): Promise<GetAPContactType> {
    const response = await apApiClient.post<{ contact: GetAPContactType }>(
      "/contacts/upsert/",
      { ...cleanData(data), locationId }
    );
    return response.data.contact;
  }

  /**
   * Update an existing contact
   * @param id - Contact ID
   * @param data - Contact data to update
   * @returns Promise with updated contact
   */
  async update(id: string, data: PostAPContactType): Promise<GetAPContactType> {
    const response = await apApiClient.put<{ contact: GetAPContactType }>(
      `/contacts/${id}`,
      cleanData(data)
    );
    return response.data.contact;
  }

  /**
   * Delete a contact
   * @param id - Contact ID
   * @returns Promise with deletion result
   */
  async delete(id: string): Promise<boolean> {
    await apApiClient.delete(`/contacts/${id}/`);
    return true;
  }

  /**
   * Get appointments for a contact
   * @param contactId - Contact ID
   * @returns Promise with appointments array
   */
  async appointments(contactId: string): Promise<GetAPAppointmentType[]> {
    const response = await apApiClient.get<{
      appointments: GetAPAppointmentType[];
    }>(
      `/contacts/${contactId}/appointments/`,
      undefined,
      `ap:contact:${contactId}:appointments`
    );
    return response.data.appointments;
  }

  /**
   * Get all contacts with pagination and filtering
   * @param params - Query parameters
   * @param locationId - Location ID
   * @returns Promise with contacts array
   */
  async all(
    params: {
      limit?: number;
      query?: string;
      startAfter?: number;
      startAfterId?: string;
    } = {},
    locationId: string
  ): Promise<GetAPContactType[]> {
    const queryParams = {
      ...params,
      locationId,
    };

    const response = await apApiClient.get<{ contacts: GetAPContactType[] }>(
      "/contacts/",
      cleanData(queryParams),
      `ap:contacts:${JSON.stringify(queryParams)}`
    );
    return response.data.contacts;
  }

  /**
   * Search for contacts
   * @param query - Search query
   * @param locationId - Location ID
   * @param limit - Number of results to return
   * @returns Promise with contacts array
   */
  async searchContacts(
    query: string,
    locationId: string,
    limit: number = 20
  ): Promise<GetAPContactType[]> {
    return this.all({ query, limit }, locationId);
  }

  /**
   * Get contacts with pagination
   * @param locationId - Location ID
   * @param limit - Number of results to return
   * @param startAfter - Start after this number
   * @param startAfterId - Start after this ID
   * @returns Promise with contacts array
   */
  async getPaginatedContacts(
    locationId: string,
    limit: number = 20,
    startAfter?: number,
    startAfterId?: string
  ): Promise<GetAPContactType[]> {
    return this.all({ limit, startAfter, startAfterId }, locationId);
  }
}

// Export singleton instance
export const apContactRequest = new APContactRequest();
