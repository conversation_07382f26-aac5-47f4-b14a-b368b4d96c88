CREATE TABLE "ap_custom_fields" (
	"id" varchar(255) PRIMARY KEY NOT NULL,
	"created_at" timestamp DEFAULT now() NOT NULL,
	"updated_at" timestamp DEFAULT now() NOT NULL,
	"ap_id" varchar(255),
	"name" varchar(255) NOT NULL,
	"config" jsonb,
	CONSTRAINT "ap_custom_fields_ap_id_unique" UNIQUE("ap_id")
);
--> statement-breakpoint
CREATE TABLE "appointments" (
	"id" varchar(255) PRIMARY KEY NOT NULL,
	"created_at" timestamp DEFAULT now() NOT NULL,
	"updated_at" timestamp DEFAULT now() NOT NULL,
	"ap_id" varchar(255) NOT NULL,
	"cc_id" integer NOT NULL,
	"patient_id" varchar(255),
	"ap_updated_at" timestamp,
	"cc_updated_at" timestamp,
	"ap_data" jsonb,
	"cc_data" jsonb,
	"ap_note_id" text,
	CONSTRAINT "appointments_ap_id_unique" UNIQUE("ap_id"),
	CONSTRAINT "appointments_cc_id_unique" UNIQUE("cc_id")
);
--> statement-breakpoint
CREATE TABLE "cc_custom_fields" (
	"id" varchar(255) PRIMARY KEY NOT NULL,
	"created_at" timestamp DEFAULT now() NOT NULL,
	"updated_at" timestamp DEFAULT now() NOT NULL,
	"cc_id" integer,
	"name" varchar(255) NOT NULL,
	"config" jsonb,
	CONSTRAINT "cc_custom_fields_cc_id_unique" UNIQUE("cc_id")
);
--> statement-breakpoint
CREATE TABLE "error_logs" (
	"id" varchar(255) PRIMARY KEY NOT NULL,
	"created_at" timestamp DEFAULT now() NOT NULL,
	"updated_at" timestamp DEFAULT now() NOT NULL,
	"message" text NOT NULL,
	"stack" text,
	"type" varchar(255) NOT NULL,
	"data" jsonb
);
--> statement-breakpoint
CREATE TABLE "patients" (
	"id" varchar(255) PRIMARY KEY NOT NULL,
	"created_at" timestamp DEFAULT now() NOT NULL,
	"updated_at" timestamp DEFAULT now() NOT NULL,
	"ap_id" varchar(255) NOT NULL,
	"cc_id" integer NOT NULL,
	"email" varchar(255),
	"phone" varchar(255),
	"ap_updated_at" timestamp,
	"cc_updated_at" timestamp,
	"ap_data" jsonb,
	"cc_data" jsonb,
	CONSTRAINT "patients_ap_id_unique" UNIQUE("ap_id"),
	CONSTRAINT "patients_cc_id_unique" UNIQUE("cc_id")
);
--> statement-breakpoint
ALTER TABLE "appointments" ADD CONSTRAINT "appointments_patient_id_patients_id_fk" FOREIGN KEY ("patient_id") REFERENCES "public"."patients"("id") ON DELETE no action ON UPDATE no action;