/**
 * Recursively removes null, undefined, empty strings, empty arrays, and empty objects from arrays and objects.
 *
 * @param data - The data to clean (can be any type)
 * @returns Cleaned data with null/undefined/empty values removed
 *
 * @example
 * ```typescript
 * // Clean nested object
 * const dirtyObj = {
 *   name: "<PERSON>",
 *   age: null,
 *   email: "",
 *   address: {
 *     street: "123 Main St",
 *     city: null,
 *     zip: undefined
 *   },
 *   hobbies: ["reading", null, "", undefined, "gaming"]
 * };
 *
 * const cleanObj = cleanData(dirtyObj);
 * // Result: {
 * //   name: "<PERSON>",
 * //   address: { street: "123 Main St" },
 * //   hobbies: ["reading", "gaming"]
 * // }
 *
 * // Clean nested array
 * const dirtyArray = [
 *   "hello",
 *   null,
 *   ["world", null, ""],
 *   { name: "<PERSON>", age: null },
 *   undefined
 * ];
 *
 * const cleanArray = cleanData(dirtyArray);
 * // Result: ["hello", ["world"], { name: "<PERSON>" }]
 * ```
 */
const cleanData = <T>(data: T): T => {
	// Handle null/undefined values
	if (data === null || data === undefined) {
		return data;
	}

	// Handle arrays
	if (Array.isArray(data)) {
		const filteredArray = data
			.map((item) => cleanData(item))
			.filter((item) => {
				if (item === null || item === undefined) return false;
				if (typeof item === "string") return item.trim() !== "";
				if (Array.isArray(item)) return item.length > 0;
				if (typeof item === "object") return Object.keys(item).length > 0;
				return true;
			});

		return filteredArray as T;
	}

	// Handle objects
	if (typeof data === "object" && data !== null) {
		const cleanedObject: Record<string, any> = {};

		for (const [key, value] of Object.entries(data)) {
			const cleanedValue = cleanData(value);

			// Skip null/undefined values
			if (cleanedValue === null || cleanedValue === undefined) continue;

			// Skip empty strings
			if (typeof cleanedValue === "string" && cleanedValue.trim() === "")
				continue;

			// Skip empty arrays
			if (Array.isArray(cleanedValue) && cleanedValue.length === 0) continue;

			// Skip empty objects
			if (
				typeof cleanedValue === "object" &&
				!Array.isArray(cleanedValue) &&
				Object.keys(cleanedValue).length === 0
			)
				continue;

			cleanedObject[key] = cleanedValue;
		}

		return cleanedObject as T;
	}

	// Return primitive values as-is
	return data;
};

export default cleanData;
