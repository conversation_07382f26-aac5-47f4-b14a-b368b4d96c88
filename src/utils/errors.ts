/**
 * Create a standardized error with status code
 */
export function createError(
	message: string,
	status: number = 500,
): Error & { status: number } {
	const error = new Error(message) as <PERSON><PERSON><PERSON> & { status: number };
	error.status = status;
	return error;
}

/**
 * Check if an error is a known HTTP error with status
 */
export function isHttpError(
	error: unknown,
): error is Error & { status: number } {
	return (
		error instanceof Error &&
		"status" in error &&
		typeof (error as Error & { status: number }).status === "number"
	);
}

/**
 * Extract status code from error, defaulting to 500
 */
export function getErrorStatus(error: unknown): number {
	if (isHttpError(error)) {
		return error.status;
	}
	return 500;
}

/**
 * Extract error message safely
 */
export function getErrorMessage(error: unknown): string {
	if (error instanceof Error) {
		return error.message;
	}
	return String(error);
}
