/**
 * Simple logger utility for webhook processing
 * In production, this would integrate with your logging service
 */

export interface LogContext {
	[key: string]: unknown;
}

export const logger = {
	info: (message: string, context?: LogContext) => {
		console.log(
			JSON.stringify({
				level: "info",
				message,
				timestamp: new Date().toISOString(),
				...context,
			}),
		);
	},

	warn: (message: string, context?: LogContext) => {
		console.warn(
			JSON.stringify({
				level: "warn",
				message,
				timestamp: new Date().toISOString(),
				...context,
			}),
		);
	},

	error: (message: string, context?: LogContext) => {
		console.error(
			JSON.stringify({
				level: "error",
				message,
				timestamp: new Date().toISOString(),
				...context,
			}),
		);
	},

	debug: (message: string, context?: LogContext) => {
		console.debug(
			JSON.stringify({
				level: "debug",
				message,
				timestamp: new Date().toISOString(),
				...context,
			}),
		);
	},
};
