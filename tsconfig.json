{"compilerOptions": {"target": "ESNext", "module": "ESNext", "moduleResolution": "<PERSON><PERSON><PERSON>", "strict": true, "skipLibCheck": true, "lib": ["ESNext"], "jsx": "react-jsx", "jsxImportSource": "hono/jsx", "baseUrl": ".", "paths": {"@/*": ["./src/*"], "@database": ["./src/database"], "@database/*": ["./src/database/*"], "@config": ["./src/utils/configs"], "@utils": ["./src/utils"], "@utils/*": ["./src/utils/*"], "@lib": ["./src/lib"], "@lib/*": ["./src/lib/*"], "@libCC": ["./src/lib/cc"], "@libCC/*": ["./src/lib/cc/*"], "@libAP": ["./src/lib/ap"], "@libAP/*": ["./src/lib/ap/*"]}}, "exclude": ["old"]}