# Data Sync System Implementation Roadmap

## Project Overview
Build a complete bidirectional data synchronization system between CC (Client Care) and AP (Active Campaign) platforms using the core business logic from the old codebase, adapted to Cloudflare Workers + Hono architecture.

## Architecture Decision
- **Cloudflare Workers**: Using existing Hono framework setup
- **Database**: Existing Drizzle ORM with PostgreSQL (Neon)
- **Core Logic**: Port the essential sync functions from `/old/helpers/`
- **Webhook-based**: Receive events from both platforms via HTTP endpoints (replacing Socket.io)

## Current Status ✅
- **Database Schema**: Already implemented and migrated
  - `patients` table (CC ↔ AP contact mapping)
  - `appointments` table (appointment mapping)
  - `ap_custom_fields` & `cc_custom_fields` tables
  - `error_logs` table for error tracking
- **HTTP Server**: Hono framework already set up
- **Database**: Drizzle ORM with Neon PostgreSQL

---

## Phase 1: Core Job Processing Functions (Port from Old Codebase `/old`)

### 1.1 Patient Processing Functions
- [ ] **ProcessPatientCreate** (from `old/Jobs/ProcessPatientCreate.ts`)
  - [ ] Receive: `{ payload: GetCCPatientType, auth: number }`
  - [ ] Check existing contact by `ccId` in database
  - [ ] Implement skip logic to prevent duplicate processing
  - [ ] Use `Contact.searchCreateOrUpdate()` for duplicate handling
  - [ ] Call `updateOrCreateContact(contact)` to sync to AP
  - [ ] Handle errors with proper logging

- [ ] **ProcessPatientUpdate** (from `old/Jobs/ProcessPatientUpdate.ts`)
  - [ ] Similar to ProcessPatientCreate but for updates
  - [ ] Handle existing contact updates
  - [ ] Sync updated data to AP

### 1.2 Appointment Processing Functions
- [ ] **ProcessCcAppointmentCreate** (from `old/Jobs/ProcessCcAppointmentCreate.ts`)
  - [ ] Receive: `{ payload: GetCCAppointmentType, auth: number }`
  - [ ] Check existing appointment by `ccId`
  - [ ] Use `Appointment.updateOrCreate()` with CC data
  - [ ] Transform `startsAt`/`endsAt` to DateTime objects
  - [ ] Call `syncCCtoAPAppointment(appointment)` to sync to AP
  - [ ] Handle errors with try-catch and logging

- [ ] **ProcessCcAppointmentUpdate** (from `old/Jobs/ProcessCcAppointmentUpdate.ts`)
  - [ ] Implement skip logic to prevent processing loops
  - [ ] Find existing appointment by `ccId`
  - [ ] Update local appointment data with new CC data
  - [ ] Call `syncCCtoAPAppointment(appointment)` to sync to AP

- [ ] **ProcessCcAppointmentDelete** (from `old/Jobs/ProcessCcAppointmentDelete.ts`)
  - [ ] Receive: `{ payload: number (appointment ID), auth: number }`
  - [ ] Find appointment by `ccId`
  - [ ] Call `deleteAppointmentFromAP(appointment)` to delete from AP
  - [ ] Update AP note with deletion information
  - [ ] Clean up local appointment record

### 1.3 Financial Data Processing Functions
- [ ] **ProcessCcInvoice** (from `old/Jobs/ProcessCcInvoice.ts`)
  - [ ] Receive: `{ payload: SocketCCInvoice, auth: number }`
  - [ ] Find contact by `ccId` from invoice patient
  - [ ] Calculate invoice data (amounts, discounts, products, diagnoses)
  - [ ] Transform to AP custom fields format
  - [ ] Update AP contact custom fields with invoice data

- [ ] **ProcessCcPayment** (from `old/Jobs/ProcessCcPayment.ts`)
  - [ ] Receive: `{ payload: SocketCCPayment, auth: number }`
  - [ ] Find contact by `ccId` from payment patient
  - [ ] Call `syncInvoice(contact)` to recalculate financial data
  - [ ] Handle LTV calculations and payment status updates

- [ ] **ProcessInvoicePayment** (from `old/Jobs/ProcessInvoicePayment.ts`)
  - [ ] Handle combined invoice/payment events
  - [ ] Trigger financial data recalculation

### 1.4 Skip Logic Implementation
- [ ] **Skip Model Functions** (from old Skip model)
  - [ ] `hasProcessPatientCreate(apId)` - Prevent duplicate patient creation
  - [ ] `hasProcessAppointmentUpdate(ccId)` - Prevent appointment update loops
  - [ ] `putProcessAppointmentCreate(apId)` - Mark appointment as processed
  - [ ] `putProcessAppointmentUpdate(ccId)` - Mark appointment update as processed

---

## Phase 2: Core Helper Functions (Port from `/old/helpers/`)

### 2.1 Contact/Patient Sync Functions
- [ ] **updateOrCreateContact** (from `old/helpers/ap.ts`)
  - [ ] Transform CC patient data to AP contact format
  - [ ] Use `contactReq.upsert()` for new contacts
  - [ ] Use `contactReq.update()` for existing contacts
  - [ ] Sync custom fields with `syncCCtoAPCustomfields`
  - [ ] Sync financial data with `syncInvoicePayments`
  - [ ] Handle duplicate prevention using existing `patients` table

- [ ] **searchPatient, createPatientToCC, updatePatientToCC** (from `old/helpers/cc.ts`)
  - [ ] Search for existing patient by email/phone
  - [ ] Create new patient if not found
  - [ ] Update existing patient with AP data
  - [ ] Sync custom fields with `syncApToCcCustomfields`

### 2.2 Appointment Sync Functions
- [ ] **syncCCtoAPAppointment** (from `old/helpers/ap.ts`)
  - [ ] Route to `createAppointmentToAP` or `updateAppointmentToAP`
  - [ ] Handle appointment data transformation
  - [ ] Sync appointment custom fields
  - [ ] Handle service mapping and resource allocation

- [ ] **createAppointmentToAP, updateAppointmentToAP** (from `old/helpers/ap.ts`)
  - [ ] Create/update appointments in AP
  - [ ] Handle appointment status synchronization
  - [ ] Link to existing contacts using `patients` table

- [ ] **createAppointmentToCC, updateAppointmentToCC** (from `old/helpers/cc.ts`)
  - [ ] Create/update appointments in CC
  - [ ] Link to existing patients
  - [ ] Handle appointment cancellation

### 2.3 Custom Field Sync Functions
- [ ] **syncCCtoAPCustomfields** (from `old/helpers/ap.ts`)
  - [ ] Map CC custom fields to AP format
  - [ ] Handle field creation if not exists
  - [ ] Update contact custom fields in AP

- [ ] **syncApToCcCustomfields** (from `old/helpers/cc.ts`)
  - [ ] Map AP custom fields to CC format
  - [ ] Handle field mapping and value conversion
  - [ ] Update patient custom fields in CC

### 2.4 Financial Data Sync Functions
- [ ] **syncInvoicePayments, syncInvoice, syncPayment, syncLtv** (from `old/helpers/ap.ts`)
  - [ ] Sync invoices from CC to AP custom fields
  - [ ] Sync payments from CC to AP custom fields
  - [ ] Calculate and sync LTV (Lifetime Value)
  - [ ] Update AP contact custom fields with financial data

---

## Phase 3: Platform Integration Layer

### 3.1 CC Platform Integration
- [ ] **CC Request Layer** (`src/lib/cc/requests/`)
  - [ ] `CCPatientRequest.ts`: Patient CRUD operations (from old `patientReq`)
  - [ ] `CCAppointmentRequest.ts`: Appointment CRUD operations (from old `ccAppointmentReq`)
  - [ ] `CCCustomFieldRequest.ts`: Custom field operations (from old `ccCustomfieldReq`)
  - [ ] `CCServiceRequest.ts`: Service operations (from old `serviceReq`)
  - [ ] `CCInvoiceRequest.ts`: Invoice operations (from old `invoiceReq`)
  - [ ] `CCPaymentRequest.ts`: Payment operations (from old `paymentReq`)
  - [ ] `CCUserRequest.ts`: User operations (from old `ccUserReq`)
  - [ ] `CCLocationRequest.ts`: Location operations (from old `ccLocationReq`)
  - [ ] `CCResourceRequest.ts`: Resource operations (from old `resourceReq`)

- [ ] **CC Types** (`src/lib/cc/CCTypes.ts`)
  - [ ] ✅ Webhook types already added
  - [ ] All existing types already present

### 3.2 AP Platform Integration
- [ ] **AP Request Layer** (`src/lib/ap/requests/`)
  - [ ] `APContactRequest.ts`: Contact CRUD operations (from old `contactReq`)
  - [ ] `APAppointmentRequest.ts`: Appointment CRUD operations (from old `apAppointmentReq`)
  - [ ] `APCustomFieldRequest.ts`: Custom field operations (from old `apCustomfield`)
  - [ ] `APNoteRequest.ts`: Note operations (from old `apNoteReq`)

- [ ] **AP Types** (`src/lib/ap/APTypes.ts`)
  - [ ] Contact types, Appointment types, Custom field types
  - [ ] Note types, OAuth types

---

## Phase 4: Webhook Endpoints (Replace Socket.io)

### 4.1 CC Platform Webhooks
- [ ] **Single CC Webhook Endpoint**: `POST /cc/webhook`
- [ ] **CC Webhook Format** (same as old socket events):
  ```json
  {
    "event": "EntityWasCreated",
    "model": "Patient", 
    "id": 123,
    "payload": { "firstName": (...) }
  }
  ```
- [ ] **Event Routing Logic** (same as old Socket.ts):
  - [ ] `event: "EntityWasCreated"` + `model: "Patient"` → Call `ProcessPatientCreate`
  - [ ] `event: "EntityWasCreated"` + `model: "Invoice"` → Call `ProcessCcInvoice`
  - [ ] `event: "EntityWasCreated"` + `model: "Payment"` → Call `ProcessCcPayment`
  - [ ] `event: "EntityWasUpdated"` + `model: "Patient"` → Call `ProcessPatientUpdate`
  - [ ] `event: "EntityWasUpdated"` + `model: "Appointment"` → Call `ProcessCcAppointmentUpdate`
  - [ ] `event: "EntityWasDeleted"` + `model: "Appointment"` → Call `ProcessCcAppointmentDelete`
  - [ ] `event: "AppointmentWasCreated"` → Call `ProcessCcAppointmentCreate`

### 4.2 AP Platform Webhooks
- [ ] **Single AP Webhook Endpoint**: `POST /ap/webhook`
- [ ] **AP Webhook Format**: Similar structure for AP platform events
- [ ] **Event Routing Logic**:
  - [ ] Contact creation events → Call `createPatientToCC`
  - [ ] Contact update events → Call `updatePatientToCC`
  - [ ] Appointment creation events → Call `createAppointmentToCC`
  - [ ] Appointment update events → Call `updateAppointmentToCC`
  - [ ] Appointment deletion events → Call `cancelAppointmentToCC`

---

## Phase 5: Database Operations & Models

### 5.1 Database Models
- [ ] **Contact Model**: Use existing `patients` table
  - [ ] `ccId`, `apId`, `email`, `phone`
  - [ ] `apData`, `ccData`, `apUpdatedAt`, `ccUpdatedAt`
- [ ] **Appointment Model**: Use existing `appointments` table
  - [ ] `ccId`, `apId`, `patientId`, `startAt`, `endAt`
  - [ ] `apData`, `ccData`, `apUpdatedAt`, `ccUpdatedAt`
- [ ] **Skip Model**: Implement skip logic for duplicate prevention
  - [ ] Track processed operations to prevent loops
  - [ ] Use KV storage or database table

### 5.2 Database Helpers
- [ ] **Repository Pattern** (`src/lib/database/`)
  - [ ] `ContactRepository.ts`: CRUD operations for `patients` table
  - [ ] `AppointmentRepository.ts`: CRUD operations for `appointments` table
  - [ ] `CustomFieldRepository.ts`: CRUD operations for custom fields tables
  - [ ] `ErrorLogRepository.ts`: CRUD operations for `error_logs` table
  - [ ] `SkipRepository.ts`: Skip logic operations

---

## Phase 6: Error Handling & Logging

### 6.1 Error Management
- [ ] **Error Logger**: Log all sync errors to existing `error_logs` table
- [ ] **Error Recovery**: Handle failed sync operations gracefully
- [ ] **Validation**: Validate data before sync operations
- [ ] **Try-Catch Patterns**: Implement proper error handling in all functions

### 6.2 Logging Patterns
- [ ] **Console Logging**: Use `cLog()` equivalent for debugging
- [ ] **Error Logging**: Use `errorLogger` for database logging
- [ ] **Slack Logging**: Implement critical error notifications

---

## Implementation Priority

### **Phase 1 (Week 1)**: Core Job Processing Functions
1. Implement `ProcessPatientCreate` and `ProcessPatientUpdate`
2. Implement `ProcessCcAppointmentCreate`, `ProcessCcAppointmentUpdate`, `ProcessCcAppointmentDelete`
3. Implement `ProcessCcInvoice` and `ProcessCcPayment`
4. Implement skip logic functions

### **Phase 2 (Week 2)**: Core Helper Functions
1. Port `updateOrCreateContact` and patient sync functions
2. Port appointment sync functions
3. Port custom field sync functions
4. Port financial data sync functions

### **Phase 3 (Week 3)**: Platform Integration
1. Implement CC platform request layer
2. Implement AP platform request layer
3. Create TypeScript types for both platforms

### **Phase 4 (Week 4)**: Webhook Endpoints
1. Create webhook endpoints for CC platform
2. Create webhook endpoints for AP platform
3. Connect webhooks to job processing functions

### **Phase 5 (Week 5)**: Polish & Testing
1. Error handling and logging
2. Testing and validation
3. Performance optimization

---

## Success Criteria
- [ ] All core job processing functions from old codebase ported and working
- [ ] All helper functions from old codebase ported and working
- [ ] Bidirectional sync between CC and AP platforms
- [ ] Real-time webhook processing (replacing Socket.io)
- [ ] Robust error handling and logging
- [ ] No data duplication or loss
- [ ] Skip logic preventing processing loops
- [ ] High performance and reliability

---

## Technical Stack
- **Runtime**: Cloudflare Workers with TypeScript
- **Framework**: Hono (✅ Already set up)
- **Database**: PostgreSQL with Drizzle ORM (✅ Already set up)
- **Architecture**: Webhook-based event processing (replacing Socket.io)
- **Error Handling**: Centralized logging to database
- **Skip Logic**: KV storage or database table for duplicate prevention 