# Data Sync Project Context

## Project Overview
This is a data synchronization service that integrates between two platforms: CC (Client Care) and AP (Active Campaign). The service handles real-time synchronization of contacts, appointments, custom fields, notes, invoices, payments, and analytics data.

## OLD CODEBASE ANALYSIS 🔍

### Architecture Overview (Old Implementation)
The old codebase was built using **AdonisJS framework** with the following key components:

#### 1. **Framework & Infrastructure**
- **AdonisJS**: Full-stack Node.js framework
- **Bull Queue**: Job processing with Redis backend
- **Socket.io**: Real-time event listening from CC platform
- **Luxon**: DateTime handling
- **Axios**: HTTP client for API requests

#### 2. **Core Architecture Components**

**Startup Files (`/old/start/`):**
- `kernel.ts`: Middleware registration and server setup
- `routes.ts`: HTTP route definitions for webhooks and OAuth
- `bull.ts`: Bull queue initialization and UI setup
- `jobs.ts`: Job registration for background processing
- `Socket.ts`: Real-time socket connection to CC platform

**Request Layer (`/old/request/`):**
- `request.ts`: Base HTTP request utilities with OAuth token management
- `ap.ts`: AP (Active Campaign) API request methods
- `cc.ts`: CC (Client Care) API request methods
- `index.ts`: Export aggregation

**Job Processing (`/old/Jobs/`):**
- `ProcessPatientCreate.ts`: Handle CC patient creation events
- `ProcessPatientUpdate.ts`: Handle CC patient update events
- `ProcessCcAppointmentCreate.ts`: Handle CC appointment creation
- `ProcessCcAppointmentUpdate.ts`: Handle CC appointment updates
- `ProcessCcAppointmentDelete.ts`: Handle CC appointment deletions
- `ProcessCcInvoice.ts`: Handle CC invoice events
- `ProcessCcPayment.ts`: Handle CC payment events
- `ProcessInvoicePayment.ts`: Handle invoice/payment events

**HTTP Controllers (`/old/Http/`):**
- `OAuthsController.ts`: OAuth flow for AP authentication
- `ProcessApAppointmentCreatesController.ts`: Handle AP appointment creation webhooks
- `ProcessApAppointmentUpdatesController.ts`: Handle AP appointment update webhooks
- `ProcessApAppointmentDeletesController.ts`: Handle AP appointment deletion webhooks
- `SyncServicesController.ts`: Service synchronization endpoints

**Business Logic (`/old/helpers/`):**
- `ap.ts`: AP platform business logic (619 lines)
- `cc.ts`: CC platform business logic (422 lines)

#### 3. **Key Implementation Patterns**

**Real-time Event Processing:**
```typescript
// Socket event listeners for CC platform
socket.on('mobimed:App\\Events\\EntityWasCreated', async (data) => {
  switch (data.type) {
    case 'patient':
      Bull.add('ProcessPatientCreate', { payload: data.payload, auth: auth.id })
      break
    case 'appointment':
      Bull.add('ProcessCcAppointmentCreate', { payload: data.payload, auth: auth.id })
      break
  }
})
```

**OAuth Token Management:**
```typescript
// Automatic token refresh with request queuing
if (auth.tokenExpire < DateTime.now().minus({ minutes: 5 })) {
  await refreshAPToken(auth)
}
```

**Job-based Processing:**
```typescript
// Background job processing for data sync
export default class ProcessPatientCreate implements JobContract {
  public async handle(job: { data: { payload: GetCCPatientType; auth: number } }) {
    const { payload, auth } = job.data
    await setAPIAuth(auth)
    // Business logic for patient creation
  }
}
```

**Webhook Processing:**
```typescript
// HTTP endpoints for AP platform webhooks
Route.post('/:location/ap/appointment/create', 'ProcessApAppointmentCreatesController.Create')
Route.post('/:location/ap/appointment/update', 'ProcessApAppointmentUpdatesController.Update')
```

#### 4. **Data Flow Architecture**

**CC → AP Flow:**
1. CC platform sends real-time events via Socket.io
2. Events are queued as Bull jobs
3. Jobs process data and sync to AP via API calls
4. Custom fields, appointments, contacts are synchronized

**AP → CC Flow:**
1. AP platform sends webhooks to HTTP endpoints
2. Controllers process webhook data
3. Data is synced to CC via API calls
4. Patients, appointments are created/updated in CC

#### 5. **Key Features Implemented**

**Contact/Patient Sync:**
- Bidirectional contact creation and updates
- Custom field synchronization
- Email and phone matching
- Duplicate prevention

**Appointment Sync:**
- Real-time appointment creation/updates/deletions
- Service mapping between platforms
- Resource allocation
- Status synchronization

**Custom Fields:**
- Dynamic custom field creation
- Value mapping between platforms
- Field type handling
- Bulk synchronization

**Financial Data:**
- Invoice synchronization
- Payment processing
- LTV (Lifetime Value) calculations
- Revenue analytics

**Authentication:**
- OAuth 2.0 flow for AP
- Token refresh management
- Multi-location support
- API credential management

## Current Status - COMPLETED ✅

### AP Services Migration - FULLY COMPLETED
All AP services have been successfully migrated from the old codebase with:
- ✅ **Real business logic** (no mocks or placeholders)
- ✅ **Strict TypeScript typing** (no `any` or implicit `any`)
- ✅ **All linter errors fixed**
- ✅ **All type errors resolved**
- ✅ **Proper JSDoc documentation**
- ✅ **Clean code organization**

#### Completed Services:
1. **APContactService.ts** - Contact sync, custom field sync, financial sync, analytics
2. **APAppointmentService.ts** - Appointment creation, update, deletion, custom field sync
3. **APCustomFieldService.ts** - Custom field creation, update, mapping, sync
4. **APNoteService.ts** - Note creation, update, deletion, specialized note types

### Technical Achievements:
- **Zero `any` types**: All services use strict domain-specific types
- **Type safety**: Full TypeScript compliance with no type errors
- **Linter clean**: All Biome linting rules satisfied
- **Error handling**: Centralized error logging with ErrorLogger
- **Request layer integration**: Proper integration with AP request classes
- **CC types integration**: Uses domain types from `@libCC/CCTypes`
- **Clean imports**: Organized imports with proper type imports

### Architecture:
- **Service Layer**: Business logic in service classes
- **Request Layer**: API communication through request classes
- **Type Safety**: Strict typing throughout the codebase
- **Error Handling**: Centralized error logging and handling
- **Configuration**: Environment-based configuration management

## Key Files:
- `src/lib/ap/services/` - All AP service implementations
- `src/lib/ap/requests/` - AP API request layer
- `src/lib/ap/APTypes.ts` - AP domain types
- `src/lib/cc/CCTypes.ts` - CC domain types
- `src/utils/errorLogger.ts` - Centralized error handling

## Next Steps:
The AP services migration is complete. The system is ready for:
- Integration testing
- Performance optimization
- Additional feature development
- Production deployment

## Package Manager:
Using `pnpm` for dependency management (confirmed via `pnpm-lock.yaml`)

---
*Last updated: Comprehensive old codebase analysis completed - All AP services fully migrated with strict typing and clean code* 